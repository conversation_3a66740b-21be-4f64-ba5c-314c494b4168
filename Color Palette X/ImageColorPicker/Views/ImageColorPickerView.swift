//
//  ImageColorPickerView.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import SwiftUI
import PhotosUI

/// 图片取色主入口界面
/// 
/// 作为功能入口点，集成底部弹窗和导航逻辑
struct ImageColorPickerView: View {
    
    // MARK: - 视图模型
    
    @State private var viewModel = ImageColorPickerViewModel()
    
    // MARK: - 初始化
    
    init() {
        AppLogger.info("ImageColorPickerView 初始化", category: .ui)
    }
    
    // MARK: - 视图
    
    var body: some View {
        NavigationStack(path: $viewModel.navigationPath) {
            ZStack {
                // 背景渐变
                backgroundGradient
                
                // 主要内容
                mainContent
            }
            .navigationTitle(LocalizedStringKey("image_color_extractor"))
            .navigationBarTitleDisplayMode(.large)
            .navigationDestination(for: String.self) { destination in
                destinationView(for: destination)
            }
        }
        .sheet(isPresented: $viewModel.isBottomSheetPresented) {
            bottomSheet
        }
        .sheet(isPresented: $viewModel.isCameraPresented) {
            cameraSheet
        }
        .sheet(isPresented: $viewModel.isPhotoPickerPresented) {
            photoPickerSheet
        }
        .alert(
            LocalizedStringKey("error"),
            isPresented: $viewModel.showErrorAlert
        ) {
            Button(LocalizedStringKey("ok")) {
                viewModel.showErrorAlert = false
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .onAppear {
            AppLogger.debug("ImageColorPickerView 显示", category: .ui)
        }
        .onDisappear {
            AppLogger.debug("ImageColorPickerView 隐藏", category: .ui)
        }
    }
    
    // MARK: - 子视图
    
    /// 背景渐变
    private var backgroundGradient: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(.systemBackground),
                Color.indigo.opacity(0.05),
                Color.purple.opacity(0.05)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    /// 主要内容
    private var mainContent: some View {
        VStack(spacing: 32) {
            // 顶部说明
            headerSection
            
            // 中央图标和按钮
            centerSection
            
            // 功能介绍
            featuresSection
            
            Spacer()
        }
        .padding(.horizontal, 24)
        .padding(.top, 20)
    }
    
    /// 头部说明区域
    private var headerSection: some View {
        VStack(spacing: 12) {
            Text(LocalizedStringKey("extract_colors_from_images"))
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundStyle(
                    LinearGradient(
                        colors: [.indigo, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .multilineTextAlignment(.center)
            
            Text(LocalizedStringKey("choose_image_source_description"))
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(3)
        }
    }
    
    /// 中央区域
    private var centerSection: some View {
        VStack(spacing: 24) {
            // 主图标
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.indigo.opacity(0.2),
                                Color.purple.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                
                Image(systemName: "photo.fill")
                    .font(.system(size: 50, weight: .medium))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.indigo, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            }
            .shadow(color: .indigo.opacity(0.2), radius: 20, x: 0, y: 10)
            
            // 主要操作按钮
            Button(action: {
                viewModel.showImageSourceOptions()
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title3)
                    
                    Text(LocalizedStringKey("select_image"))
                        .font(.system(size: 18, weight: .semibold))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: [.indigo, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .shadow(color: .indigo.opacity(0.3), radius: 8, x: 0, y: 4)
                )
            }
            .buttonStyle(PlainButtonStyle())
            .scaleEffect(viewModel.isProcessingSelection ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: viewModel.isProcessingSelection)
            .disabled(viewModel.isProcessingSelection)
            .accessibilityLabel(LocalizedStringKey("select_image_for_color_extraction"))
        }
    }
    
    /// 功能介绍区域
    private var featuresSection: some View {
        VStack(spacing: 16) {
            Text(LocalizedStringKey("features"))
                .font(.headline)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ImagePickerFeatureCard(
                    icon: "camera.fill",
                    title: LocalizedStringKey("take_photo"),
                    description: LocalizedStringKey("capture_new_image"),
                    color: .blue
                )

                ImagePickerFeatureCard(
                    icon: "photo.on.rectangle",
                    title: LocalizedStringKey("from_library"),
                    description: LocalizedStringKey("choose_existing_image"),
                    color: .green
                )

                ImagePickerFeatureCard(
                    icon: "paintpalette.fill",
                    title: LocalizedStringKey("extract_colors"),
                    description: LocalizedStringKey("ai_powered_extraction"),
                    color: .purple
                )

                ImagePickerFeatureCard(
                    icon: "square.and.arrow.up",
                    title: LocalizedStringKey("share_results"),
                    description: LocalizedStringKey("export_color_palette"),
                    color: .orange
                )
            }
        }
    }
    
    /// 底部弹窗
    private var bottomSheet: some View {
        ImageSourceBottomSheet(
            onSelection: { source in
                viewModel.handleImageSourceSelection(source)
            },
            onDismiss: {
                viewModel.isBottomSheetPresented = false
            }
        )
    }
    
    /// 相机弹窗
    private var cameraSheet: some View {
        CameraPickerView(
            onImagePicked: { info in
                viewModel.handleCameraResult(info)
                viewModel.isCameraPresented = false
            },
            onCancel: {
                viewModel.handleCameraCancel()
                viewModel.isCameraPresented = false
            }
        )
    }
    
    /// 相册选择弹窗
    private var photoPickerSheet: some View {
        PhotoPickerView(
            onImagePicked: { results in
                viewModel.handlePhotoPickerResult(results)
                viewModel.isPhotoPickerPresented = false
            },
            onCancel: {
                viewModel.handlePhotoPickerCancel()
                viewModel.isPhotoPickerPresented = false
            }
        )
    }
    
    /// 目标视图
    /// - Parameter destination: 目标标识
    /// - Returns: 对应的视图
    @ViewBuilder
    private func destinationView(for destination: String) -> some View {
        switch destination {
        case "ColorExtraction":
            if let selectedImage = viewModel.selectedImage {
                ColorExtractionView(
                    image: selectedImage,
                    onSelectNewImage: {
                        viewModel.resetSelection()
                        viewModel.showImageSourceOptions()
                    }
                )
            } else {
                Text(LocalizedStringKey("no_image_selected"))
                    .foregroundColor(.secondary)
            }
            
        default:
            Text("Unknown destination: \(destination)")
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - 功能卡片

/// 功能介绍卡片组件
private struct ImagePickerFeatureCard: View {
    let icon: String
    let title: LocalizedStringKey
    let description: LocalizedStringKey
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(title)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.primary)
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - 相机选择器包装

/// 相机选择器包装视图
private struct CameraPickerView: UIViewControllerRepresentable {
    let onImagePicked: ([UIImagePickerController.InfoKey: Any]) -> Void
    let onCancel: () -> Void
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        do {
            let picker = try CameraService.shared.createCameraController()
            picker.delegate = context.coordinator
            return picker
        } catch {
            AppLogger.error("创建相机控制器失败: \(error.localizedDescription)", category: .ui)
            // 返回一个空的控制器，实际应用中应该处理这个错误
            return UIImagePickerController()
        }
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraPickerView
        
        init(_ parent: CameraPickerView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
            parent.onImagePicked(info)
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.onCancel()
        }
    }
}

// MARK: - 相册选择器包装

/// 相册选择器包装视图
private struct PhotoPickerView: UIViewControllerRepresentable {
    let onImagePicked: ([PHPickerResult]) -> Void
    let onCancel: () -> Void
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        let picker = PhotoLibraryService.shared.createPhotoPickerController()
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: PhotoPickerView
        
        init(_ parent: PhotoPickerView) {
            self.parent = parent
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            if results.isEmpty {
                parent.onCancel()
            } else {
                parent.onImagePicked(results)
            }
        }
    }
}

// MARK: - 预览

#Preview("ImageColorPickerView") {
    ImageColorPickerView()
}

#Preview("ImageColorPickerView - Dark Mode") {
    ImageColorPickerView()
        .preferredColorScheme(.dark)
}