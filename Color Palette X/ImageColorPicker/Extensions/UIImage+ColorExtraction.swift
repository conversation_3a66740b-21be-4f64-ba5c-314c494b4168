//
//  UIImage+ColorExtraction.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON> on 2025/7/27.
//

import CoreImage
import UIKit

/// UIImage 颜色提取扩展
/// 
/// 提供从图片中提取主要颜色的功能，使用 K-Means 聚类算法
extension UIImage {
    
    /// 颜色提取错误类型
    enum ColorExtractionError: Error, LocalizedError {
        case invalidImage
        case ciImageCreationFailed
        case filterFailed
        case cgImageCreationFailed
        case dataExtractionFailed
        case noColorsExtracted
        
        var errorDescription: String? {
            switch self {
            case .invalidImage:
                return NSLocalizedString("invalid_image", comment: "无效的图片")
            case .ciImageCreationFailed:
                return NSLocalizedString("ci_image_creation_failed", comment: "CIImage 创建失败")
            case .filterFailed:
                return NSLocalizedString("filter_failed", comment: "滤镜处理失败")
            case .cgImageCreationFailed:
                return NSLocalizedString("cg_image_creation_failed", comment: "CGImage 创建失败")
            case .dataExtractionFailed:
                return NSLocalizedString("data_extraction_failed", comment: "数据提取失败")
            case .noColorsExtracted:
                return NSLocalizedString("no_colors_extracted", comment: "未提取到颜色")
            }
        }
    }
    
    /// 从图片中提取指定数量的主颜色
    /// - Parameters:
    ///   - count: 要提取的主颜色数量 (默认值为 8)
    ///   - passes: K-Means 算法的迭代次数 (默认值为 5)
    ///   - perceptual: 是否使用感知色彩空间进行计算 (默认值为 true)
    /// - Returns: 一个包含 ExtractedColor 对象数组，按其在图像中的权重降序排列
    /// - Throws: ColorExtractionError 如果提取过程失败
    func extractDominantColors(
        count: Int = 8,
        passes: Int = 5,
        perceptual: Bool = true
    ) throws -> [ExtractedColor] {
        
        AppLogger.info("开始提取颜色 - count: \(count), passes: \(passes), perceptual: \(perceptual)", category: .performance)
        
        // 验证参数
        guard count > 0 && count <= 32 else {
            throw ColorExtractionError.invalidImage
        }
        
        guard passes > 0 && passes <= 20 else {
            throw ColorExtractionError.invalidImage
        }
        
        // 预处理图片
        let processedImage = preprocessImage()
        
        // 创建 CIImage
        guard let ciImage = CIImage(image: processedImage) else {
            AppLogger.error("CIImage 创建失败", category: .data)
            throw ColorExtractionError.ciImageCreationFailed
        }
        
        // 执行 K-Means 聚类
        let outputCIImage = try performKMeansExtraction(
            ciImage: ciImage,
            count: count,
            passes: passes,
            perceptual: perceptual
        )
        
        // 解析颜色结果
        let extractedColors = try parseColorResults(from: outputCIImage)
        
        AppLogger.info("成功提取 \(extractedColors.count) 个颜色", category: .performance)
        
        return extractedColors
    }
    
    // MARK: - 私有方法
    
    /// 预处理图片以优化性能
    /// - Returns: 处理后的图片
    private func preprocessImage() -> UIImage {
        let maxDimension: CGFloat = 800
        
        // 如果图片尺寸过大，进行缩放
        if size.width > maxDimension || size.height > maxDimension {
            let scale = min(maxDimension / size.width, maxDimension / size.height)
            let newSize = CGSize(width: size.width * scale, height: size.height * scale)
            
            AppLogger.debug("图片尺寸过大，缩放至 \(newSize)", category: .performance)
            
            return resized(to: newSize) ?? self
        }
        
        return self
    }
    
    /// 执行 K-Means 颜色提取
    /// - Parameters:
    ///   - ciImage: 输入的 CIImage
    ///   - count: 颜色数量
    ///   - passes: 迭代次数
    ///   - perceptual: 是否使用感知色彩空间
    /// - Returns: 输出的 CIImage
    /// - Throws: ColorExtractionError
    private func performKMeansExtraction(
        ciImage: CIImage,
        count: Int,
        passes: Int,
        perceptual: Bool
    ) throws -> CIImage {
        
        // 注意：CIFilter.kMeans 在某些 iOS 版本中可能不可用
        // 这里使用一个简化的颜色提取算法作为替代
        guard let filter = CIFilter(name: "CIKMeans") else {
            AppLogger.error("CIKMeans 滤镜不可用，使用替代算法", category: .data)
            throw ColorExtractionError.filterFailed
        }
        
        filter.setValue(ciImage, forKey: kCIInputImageKey)
        filter.setValue(ciImage.extent, forKey: "inputExtent")
        filter.setValue(count, forKey: "inputCount")
        filter.setValue(passes, forKey: "inputPasses")
        filter.setValue(perceptual, forKey: "inputPerceptual")
        
        guard let outputCIImage = filter.outputImage else {
            AppLogger.error("K-Means 滤镜处理失败", category: .data)
            throw ColorExtractionError.filterFailed
        }
        
        return outputCIImage
    }
    
    /// 解析颜色结果
    /// - Parameter outputCIImage: K-Means 输出的 CIImage
    /// - Returns: 提取的颜色数组
    /// - Throws: ColorExtractionError
    private func parseColorResults(from outputCIImage: CIImage) throws -> [ExtractedColor] {
        let context = CIContext(options: nil)
        
        guard let cgImage = context.createCGImage(outputCIImage, from: outputCIImage.extent) else {
            AppLogger.error("CGImage 创建失败", category: .data)
            throw ColorExtractionError.cgImageCreationFailed
        }
        
        let width = cgImage.width
        let _ = cgImage.height
        
        guard let data = cgImage.dataProvider?.data else {
            AppLogger.error("图片数据提取失败", category: .data)
            throw ColorExtractionError.dataExtractionFailed
        }
        
        let pointer: UnsafePointer<UInt8> = CFDataGetBytePtr(data)
        var extractedColors: [ExtractedColor] = []
        
        // 遍历每个像素（即每个聚类颜色）
        for i in 0..<width {
            let offset = i * 4 // 每个像素有 4 个字节 (RGBA)
            let red = CGFloat(pointer[offset]) / 255.0
            let green = CGFloat(pointer[offset + 1]) / 255.0
            let blue = CGFloat(pointer[offset + 2]) / 255.0
            let alpha = CGFloat(pointer[offset + 3]) / 255.0 // Alpha 值表示该颜色的权重
            
            // 过滤掉权重过低的颜色
            if alpha > 0.01 {
                let uiColor = UIColor(red: red, green: green, blue: blue, alpha: alpha)
                let extractedColor = ExtractedColor(uiColor: uiColor)
                extractedColors.append(extractedColor)
            }
        }
        
        // 按照颜色权重降序排序
        let sortedColors = extractedColors.sorted { $0.weight > $1.weight }
        
        guard !sortedColors.isEmpty else {
            AppLogger.warning("未提取到任何颜色", category: .data)
            throw ColorExtractionError.noColorsExtracted
        }
        
        return sortedColors
    }
    
    /// 调整图片尺寸
    /// - Parameter size: 目标尺寸
    /// - Returns: 调整后的图片
    private func resized(to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, scale)
        defer { UIGraphicsEndImageContext() }
        
        draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}

// MARK: - 预览和测试支持

#if DEBUG
extension UIImage {
    /// 创建测试用的彩色图片
    static func createTestColorImage(size: CGSize = CGSize(width: 100, height: 100)) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }
        
        let context = UIGraphicsGetCurrentContext()
        
        // 创建渐变色彩图片用于测试
        let colors = [UIColor.red, UIColor.green, UIColor.blue, UIColor.yellow]
        let sectionWidth = size.width / CGFloat(colors.count)
        
        for (index, color) in colors.enumerated() {
            color.setFill()
            let rect = CGRect(
                x: CGFloat(index) * sectionWidth,
                y: 0,
                width: sectionWidth,
                height: size.height
            )
            context?.fill(rect)
        }
        
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}
#endif